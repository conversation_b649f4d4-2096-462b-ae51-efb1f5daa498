/**
 * تحسينات الأداء للصفحات
 */

// إعدادات ISR للصفحات المختلفة
export const pageConfigs = {
  home: {
    revalidate: 900, // 15 دقيقة
    description: 'الصفحة الرئيسية - تحديث كل 15 دقيقة'
  },
  products: {
    revalidate: 1800, // 30 دقيقة
    description: 'صفحة المنتجات - تحديث كل 30 دقيقة'
  },
  categories: {
    revalidate: 3600, // ساعة
    description: 'صفحة الفئات - تحديث كل ساعة'
  },
  about: {
    revalidate: 86400, // يوم
    description: 'صفحة من نحن - تحديث كل يوم'
  },
  contact: {
    revalidate: 86400, // يوم
    description: 'صفحة اتصل بنا - تحديث كل يوم'
  }
};

// إعدادات الكاش للـ API
export const apiCacheConfigs = {
  products: {
    ttl: 1800000, // 30 دقيقة
    staleWhileRevalidate: 3600000, // ساعة
    description: 'كاش المنتجات'
  },
  categories: {
    ttl: 3600000, // ساعة
    staleWhileRevalidate: 7200000, // ساعتين
    description: 'كاش الفئات'
  },
  featured: {
    ttl: 900000, // 15 دقيقة
    staleWhileRevalidate: 1800000, // 30 دقيقة
    description: 'كاش المنتجات المميزة'
  },
  settings: {
    ttl: 3600000, // ساعة
    staleWhileRevalidate: 7200000, // ساعتين
    description: 'كاش الإعدادات'
  }
};

// دالة لتحسين الصور
export function getOptimizedImageProps(
  src: string,
  width?: number,
  height?: number,
  quality: number = 80
) {
  return {
    src,
    width,
    height,
    quality,
    loading: 'lazy' as const,
    placeholder: 'blur' as const,
    blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==',
    sizes: '(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw'
  };
}

// دالة لتحسين الخطوط - استخدام Google Fonts المحملة مسبقاً
export function preloadCriticalFonts() {
  if (typeof window === 'undefined') return;

  // تحسين عرض الخطوط المحملة من Google Fonts
  document.documentElement.style.fontDisplay = 'swap';

  // إضافة preconnect لـ Google Fonts إذا لم يكن موجوداً
  const preconnectLinks = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ];

  preconnectLinks.forEach(href => {
    if (!document.querySelector(`link[href="${href}"]`)) {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = href;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    }
  });
}

// دالة لتحسين التمرير
export function optimizeScrollPerformance() {
  if (typeof window === 'undefined') return;

  let ticking = false;

  function updateScrollPosition() {
    // تحديث موقع التمرير
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition);
      ticking = true;
    }
  }

  // إضافة مستمع التمرير المحسن
  window.addEventListener('scroll', requestTick, { passive: true });

  return () => {
    window.removeEventListener('scroll', requestTick);
  };
}

// دالة لتحسين الصور الكسولة
export function setupLazyImages() {
  if (typeof window === 'undefined') return;

  const images = document.querySelectorAll('img[data-src]');
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
            imageObserver.unobserve(img);
          }
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });

    images.forEach((img) => imageObserver.observe(img));

    return () => {
      imageObserver.disconnect();
    };
  }
}

// دالة لمراقبة Core Web Vitals
export function monitorWebVitals() {
  if (typeof window === 'undefined') return;

  // مراقبة LCP (Largest Contentful Paint)
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        console.log('🎯 LCP:', entry.startTime);
      }
    }
  });

  try {
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch {
    // Fallback for older browsers
  }

  // مراقبة CLS (Cumulative Layout Shift)
  let clsValue = 0;
  const clsObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const layoutShiftEntry = entry as PerformanceEntry & {
        hadRecentInput?: boolean;
        value?: number;
      };
      if (!layoutShiftEntry.hadRecentInput) {
        clsValue += layoutShiftEntry.value || 0;
        console.log('📏 CLS:', clsValue);
      }
    }
  });

  try {
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  } catch {
    // Fallback for older browsers
  }

  return () => {
    observer.disconnect();
    clsObserver.disconnect();
  };
}

// دالة لتحسين الذاكرة
export function optimizeMemoryUsage() {
  if (typeof window === 'undefined') return;

  // تنظيف الذاكرة عند إغلاق الصفحة
  const cleanup = () => {
    // مسح event listeners
    // تنظيف timers
    // مسح caches قديمة
  };

  window.addEventListener('beforeunload', cleanup);
  window.addEventListener('pagehide', cleanup);

  return cleanup;
}

// دالة شاملة لتهيئة جميع التحسينات
export function initializePagePerformance() {
  if (typeof window === 'undefined') return;

  console.log('🚀 تهيئة تحسينات الأداء...');

  const cleanupFunctions: (() => void)[] = [];

  // تحميل الخطوط المهمة
  preloadCriticalFonts();

  // تحسين التمرير
  const scrollCleanup = optimizeScrollPerformance();
  if (scrollCleanup) cleanupFunctions.push(scrollCleanup);

  // تحسين الصور الكسولة
  const imagesCleanup = setupLazyImages();
  if (imagesCleanup) cleanupFunctions.push(imagesCleanup);

  // مراقبة Web Vitals
  const vitalsCleanup = monitorWebVitals();
  if (vitalsCleanup) cleanupFunctions.push(vitalsCleanup);

  // تحسين الذاكرة
  const memoryCleanup = optimizeMemoryUsage();
  if (memoryCleanup) cleanupFunctions.push(memoryCleanup);

  console.log('✅ تم تهيئة تحسينات الأداء');

  // إرجاع دالة التنظيف
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  };
}
