'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * مكون صورة محسن مع تحميل كسول وتحسينات الأداء
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 80,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  style,
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);

  // تحديث المصدر عند تغيير src
  useEffect(() => {
    setImageSrc(src);
    setHasError(false);
    setIsLoading(true);
  }, [src]);

  // معالج تحميل الصورة
  const handleLoad = () => {
    setIsLoading(false);
    if (onLoad) onLoad();
  };

  // معالج خطأ الصورة
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    // استخدام صورة افتراضية عند الخطأ
    setImageSrc('/images/placeholder.jpg');
    if (onError) onError();
  };

  // إنشاء placeholder blur إذا لم يتم توفيره
  const defaultBlurDataURL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';

  // تحديد أحجام الصورة التفاعلية
  const responsiveSizes = sizes || `
    (max-width: 640px) 100vw,
    (max-width: 768px) 50vw,
    (max-width: 1024px) 33vw,
    25vw
  `;

  // إعدادات الصورة
  const imageProps = {
    src: imageSrc,
    alt,
    quality,
    onLoad: handleLoad,
    onError: handleError,
    className: `
      ${className}
      ${isLoading ? 'opacity-0' : 'opacity-100'}
      transition-opacity duration-300 ease-in-out
      ${hasError ? 'grayscale' : ''}
    `.trim(),
    style,
    priority,
    placeholder: placeholder as 'blur' | 'empty',
    blurDataURL: blurDataURL || (placeholder === 'blur' ? defaultBlurDataURL : undefined),
  };

  // إذا كانت الصورة تملأ الحاوي
  if (fill) {
    return (
      <div className="relative overflow-hidden">
        <Image
          {...imageProps}
          fill
          sizes={responsiveSizes}
        />
        {isLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
          </div>
        )}
      </div>
    );
  }

  // إذا كانت الصورة لها أبعاد محددة
  if (width && height) {
    return (
      <div className="relative" style={{ width, height }}>
        <Image
          {...imageProps}
          width={width}
          height={height}
          sizes={responsiveSizes}
        />
        {isLoading && (
          <div 
            className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
            style={{ width, height }}
          >
            <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
          </div>
        )}
      </div>
    );
  }

  // صورة تفاعلية بدون أبعاد محددة
  return (
    <div className="relative">
      <Image
        {...imageProps}
        width={width || 800}
        height={height || 600}
        sizes={responsiveSizes}
      />
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
