const mysql = require('mysql2/promise');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'droobhajer_db',
  charset: 'utf8mb4',
  timezone: '+00:00'
};

async function testDatabase() {
  let connection;
  
  try {
    console.log('🔄 الاتصال بقاعدة البيانات...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود جدول الفئات
    const [tables] = await connection.execute("SHOW TABLES LIKE 'categories'");
    console.log('📋 جداول الفئات:', tables);
    
    if (tables.length === 0) {
      console.log('❌ جدول الفئات غير موجود');
      return;
    }
    
    // التحقق من بنية الجدول
    const [structure] = await connection.execute("DESCRIBE categories");
    console.log('🏗️ بنية جدول الفئات:');
    structure.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // عد الفئات
    const [countResult] = await connection.execute(
      'SELECT COUNT(*) as total, COUNT(CASE WHEN is_active = 1 THEN 1 END) as active FROM categories WHERE deleted_at IS NULL'
    );
    
    console.log(`📊 إجمالي الفئات: ${countResult[0].total}`);
    console.log(`📊 الفئات النشطة: ${countResult[0].active}`);
    
    // عرض الفئات الموجودة
    const [categories] = await connection.execute(
      'SELECT id, name, name_ar, is_active, created_at FROM categories WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT 10'
    );
    
    console.log('📋 الفئات الموجودة:');
    if (categories.length === 0) {
      console.log('  لا توجد فئات');
    } else {
      categories.forEach(cat => {
        console.log(`  - ${cat.name_ar} (${cat.name}) - ${cat.is_active ? 'نشط' : 'غير نشط'} - ${cat.created_at}`);
      });
    }
    
  } catch (error) {
    console.error('❌ خطأ في قاعدة البيانات:', error.message);
    console.error('📝 تفاصيل الخطأ:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testDatabase();
